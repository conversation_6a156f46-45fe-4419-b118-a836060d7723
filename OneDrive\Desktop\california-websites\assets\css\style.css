/* California Driving School - Custom CSS */

/* CSS Variables for California Theme */
:root {
    --primary-color: #FF6B35;        /* California Orange */
    --secondary-color: #FFD23F;      /* California Gold */
    --accent-color: #1E3A8A;         /* Deep Blue */
    --text-dark: #1F2937;            /* Dark Gray */
    --text-light: #6B7280;           /* Light Gray */
    --white: #FFFFFF;
    --light-bg: #F9FAFB;
    --gradient-primary: linear-gradient(135deg, #FF6B35 0%, #FFD23F 100%);
    --gradient-secondary: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
    --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 14px 28px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: var(--transition);
    border: 2px solid transparent;
    cursor: pointer;
    text-align: center;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--text-dark);
}

.nav-logo img {
    height: 200px;
    width: auto;
    margin: -60px 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link.cta-btn {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 10px 20px;
    border-radius: var(--border-radius);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: var(--white);
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    margin-top: 20%;
}

.highlight {
    color: var(--secondary-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 4rem;
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: 800;
    color: var(--secondary-color);
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.5rem;
}



/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 6rem 0;
    background: var(--light-bg);
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.about-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    text-align: center;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
}

.about-card h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.about-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Programs Section */
.programs {
    padding: 6rem 0;
    background: var(--white);
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.program-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: 2.5rem;
    transition: var(--transition);
    position: relative;
    border: 2px solid transparent;
}

.program-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.program-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.program-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: var(--white);
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.program-header {
    text-align: center;
    margin-bottom: 2rem;
}

.program-header h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.program-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.5rem;
}

.currency {
    font-size: 1.2rem;
    color: var(--text-light);
}

.amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
}

.program-features ul {
    list-style: none;
    margin-bottom: 2rem;
}

.program-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.program-features i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.program-duration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 2rem;
    color: var(--text-light);
    font-weight: 500;
}

.program-duration i {
    color: var(--primary-color);
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: var(--light-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-image {
    width: 100px;
    height: 100px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2.5rem;
    color: var(--white);
}

.feature-item h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.feature-item p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    padding: 6rem 0;
    background: var(--white);
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.testimonial-card {
    display: none;
    text-align: center;
    padding: 3rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.testimonial-card.active {
    display: block;
}

.stars {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.stars i {
    color: var(--secondary-color);
    font-size: 1.2rem;
}

.testimonial-content p {
    font-size: 1.2rem;
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author-info h4 {
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

.testimonial-nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
}

.nav-dot.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Contact Section */
.contact {
    padding: 6rem 0;
    background: var(--light-bg);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    flex-shrink: 0;
}

.contact-details h3 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--text-light);
    line-height: 1.6;
}

.contact-details a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.contact-details a:hover {
    color: var(--accent-color);
}

.contact-form {
    background: var(--white);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #E5E7EB;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    /* margin-bottom: 1.5rem; */
    font-weight: 700;
    font-size: 1.2rem;
}

.footer-logo img {
    height: 200px;
    width: auto;
    margin: -60px 0;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section h3 {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.footer-contact i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Floating Elements */
.whatsapp-float {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: #25D366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
    text-decoration: none;
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    color: var(--white);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
    100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
}

.back-to-top {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-medium);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 3rem;
        margin-top: 15%;
    }

    .hero-stats {
        gap: 2rem;
    }

    .about-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .programs-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-medium);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-title {
        font-size: 2.5rem;
        margin-top: 15%;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons {
        /* flex-direction: column; */
        align-items: center;
    }

    .hero-stats {
        /* flex-direction: column; */
        gap: 1.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
    }

    .programs-grid {
        grid-template-columns: 1fr;
    }

    .program-card.featured {
        transform: none;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .whatsapp-float {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .back-to-top {
        bottom: 1rem;
        left: 1rem;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
        margin-top: 35%;
    }
    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label{
        font-size: 0.7rem;
    }

    .program-card,
    .about-card {
        padding: 2rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .testimonial-card {
        padding: 2rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}
